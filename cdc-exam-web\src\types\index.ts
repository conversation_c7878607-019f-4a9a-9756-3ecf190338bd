// 用户相关类型
export interface User {
  id: string
  username: string
  email?: string
  phone?: string
  avatar?: string
  realName?: string
  idCard?: string
  workUnit?: string
  position?: string
  permissions: string[]
  createdAt: string
  updatedAt: string
}

// 认证相关类型
export interface LoginResponse {
  token: string
  user: User
  expiresIn: number
}

export interface WeChatLoginParams {
  code: string
  state?: string
}

// 信息中心类型
export interface InfoItem {
  id: string
  title: string
  content: string
  type: 'announcement' | 'policy' | 'notification'
  status: 'published' | 'draft'
  publishTime: string
  author: string
  views: number
  attachments?: Attachment[]
  createdAt: string
  updatedAt: string
}

export interface Attachment {
  id: string
  name: string
  url: string
  size: number
  type: string
}

// 学习中心类型
export interface QuestionCategory {
  id: string
  name: string
  description: string
  questionCount: number
  difficulty: 'easy' | 'medium' | 'hard'
  icon?: string
}

export interface Question {
  id: string
  categoryId: string
  type: 'single' | 'multiple' | 'judge' | 'fill'
  title: string
  content: string
  options?: QuestionOption[]
  correctAnswer: string | string[]
  explanation?: string
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  createdAt: string
}

export interface QuestionOption {
  id: string
  label: string
  content: string
}

export interface PracticeSession {
  id: string
  categoryId: string
  questions: Question[]
  answers: Record<string, string | string[]>
  score?: number
  completedAt?: string
  duration?: number
}

// 考试中心类型
export interface Exam {
  id: string
  title: string
  description: string
  type: 'online' | 'offline'
  status: 'pending' | 'ongoing' | 'completed' | 'cancelled'
  startTime: string
  endTime: string
  duration: number // 分钟
  totalQuestions: number
  totalScore: number
  passScore: number
  allowRetake: boolean
  maxAttempts: number
  registrationDeadline: string
  requirements?: string[]
  createdAt: string
}

export interface ExamRecord {
  id: string
  examId: string
  exam: Exam
  userId: string
  status: 'registered' | 'in_progress' | 'completed' | 'absent'
  score?: number
  answers?: Record<string, string | string[]>
  startTime?: string
  endTime?: string
  duration?: number
  attempt: number
  createdAt: string
}

// 证书类型
export interface Certificate {
  id: string
  userId: string
  examId: string
  exam: Exam
  certificateNumber: string
  issueDate: string
  expiryDate?: string
  status: 'valid' | 'expired' | 'revoked'
  downloadUrl?: string
  createdAt: string
}

// 通用类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResponse<T = any> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface SearchParams {
  keyword?: string
  category?: string
  status?: string
  startDate?: string
  endDate?: string
}

// 通知类型
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  persistent?: boolean
  createdAt: string
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  requiresAuth?: boolean
  permissions?: string[]
  layout?: string
  icon?: string
  hidden?: boolean
}
